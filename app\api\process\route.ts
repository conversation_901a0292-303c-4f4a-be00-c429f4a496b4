import { NextRequest } from "next/server";
import fs from "fs/promises";
import path from "path";
import { processWithGemini } from "@/lib/gemini-client";

export const runtime = "nodejs";
export const dynamic = "force-dynamic";

interface ProcessRequest {
  files: string[];
  rule: string;
}

export async function POST(request: NextRequest) {
  const encoder = new TextEncoder();

  const stream = new ReadableStream({
    async start(controller) {
      try {
        const body: ProcessRequest = await request.json();
        const { files, rule } = body;

        // 加载规则配置
        const rulePath = path.join(process.cwd(), "rules", `${rule}.js`);
        let ruleConfig;

        try {
          const ruleContent = await fs.readFile(rulePath, "utf-8");

          // 尝试多种解析方法
          let config = null;

          // 方法1: 尝试直接require（如果可能）
          try {
            const fullPath = path.resolve(rulePath);
            delete require.cache[fullPath];
            const ruleModule = require(fullPath);
            const configName = Object.keys(ruleModule)[0];
            config = ruleModule[configName];
          } catch (requireError) {
            console.log("Require failed, trying regex parsing:", requireError.message);

            // 方法2: 使用正则表达式解析
            const match = ruleContent.match(/export\s+const\s+[^\s=]+Config\s*=\s*(\{[\s\S]*?\})\s*;?\s*$/m);

            if (match) {
              try {
                // 对于简单的配置（没有模板字符串），直接使用eval
                config = eval(`(${match[1]})`);
              } catch (evalError) {
                console.log("Eval failed:", evalError.message);
                // 如果eval失败，返回null让它使用默认配置
                config = null;
              }
            }
          }

          if (config) {
            ruleConfig = config;
          } else {
            throw new Error("Could not parse rule configuration");
          }
        } catch (error) {
          console.error("加载规则失败:", error);

          // 如果规则文件不存在或解析失败，使用默认配置
          console.log(`使用默认配置替代规则: ${rule}`);
          ruleConfig = {
            model: "gemini-2.0-flash-exp",
            config: {
              systemInstruction: `你是一个专业的内容生成助手。`,
            }
          };
        }

        const total = files.length;
        let completed = 0;

        // 并发处理文件（最多 3 个并发）
        const maxConcurrent = 3;
        const queue = [...files];
        const workers: Promise<void>[] = [];

        for (let i = 0; i < Math.min(maxConcurrent, files.length); i++) {
          workers.push(
            (async () => {
              while (queue.length > 0) {
                const filePath = queue.shift();
                if (!filePath) break;

                try {
                  // 读取输入文件
                  const inputPath = path.join(process.cwd(), "input", filePath);
                  const content = await fs.readFile(inputPath, "utf-8");

                  // 发送处理开始事件
                  controller.enqueue(
                    encoder.encode(
                      `data: ${JSON.stringify({
                        type: "progress",
                        fileName: path.basename(filePath),
                        status: "processing",
                        completed,
                        total,
                        originalContent: content,
                      })}\n\n`
                    )
                  );

                  // 使用 Gemini 处理
                  const result = await processWithGemini(
                    content,
                    ruleConfig,
                    filePath
                  );

                  if (result.success && result.content) {
                    // 保存到输出文件夹
                    const outputPath = path.join(process.cwd(), "output", filePath);
                    const outputDir = path.dirname(outputPath);

                    // 确保输出目录存在
                    await fs.mkdir(outputDir, { recursive: true });

                    // 写入文件
                    await fs.writeFile(outputPath, result.content, "utf-8");

                    completed++;

                    // 发送进度更新
                    controller.enqueue(
                      encoder.encode(
                        `data: ${JSON.stringify({
                          type: "progress",
                          fileName: path.basename(filePath),
                          status: "completed",
                          completed,
                          total,
                          generatedContent: result.content,
                        })}\n\n`
                      )
                    );
                  } else {
                    // 处理失败
                    controller.enqueue(
                      encoder.encode(
                        `data: ${JSON.stringify({
                          type: "progress",
                          fileName: path.basename(filePath),
                          status: "error",
                          completed,
                          total,
                          error: result.error || "处理失败",
                        })}\n\n`
                      )
                    );
                  }
                } catch (error: any) {
                  console.error(`处理文件 ${filePath} 失败:`, error);
                  controller.enqueue(
                    encoder.encode(
                      `data: ${JSON.stringify({
                        type: "progress",
                        fileName: path.basename(filePath),
                        status: "error",
                        completed,
                        total,
                        error: error.message,
                      })}\n\n`
                    )
                  );
                }
              }
            })()
          );
        }

        // 等待所有工作完成
        await Promise.all(workers);

        // 发送完成事件
        controller.enqueue(
          encoder.encode(
            `data: ${JSON.stringify({
              type: "complete",
              completed,
              total,
            })}\n\n`
          )
        );

        controller.close();
      } catch (error: any) {
        console.error("处理请求失败:", error);
        controller.enqueue(
          encoder.encode(
            `data: ${JSON.stringify({
              type: "error",
              message: error.message,
            })}\n\n`
          )
        );
        controller.close();
      }
    },
  });

  return new Response(stream, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      "Connection": "keep-alive",
    },
  });
}

