import { NextRequest, NextResponse } from "next/server";
import fs from "fs/promises";
import path from "path";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ name: string }> }
) {
  try {
    const { name } = await params;
    const rulePath = path.join(process.cwd(), "rules", `${name}.js`);

    try {
      console.log("Loading rule from path:", rulePath);
      const content = await fs.readFile(rulePath, "utf-8");
      console.log("File content loaded, length:", content.length);

      // 尝试多种解析方法
      let config = null;

      // 方法1: 尝试直接require（如果可能）
      try {
        const fullPath = path.resolve(rulePath);
        delete require.cache[fullPath];
        const ruleModule = require(fullPath);
        const configName = Object.keys(ruleModule)[0];
        config = ruleModule[configName];
      } catch (requireError) {
        console.log("Require failed, trying regex parsing:", requireError.message);

        // 方法2: 使用正则表达式解析
        const match = content.match(/export\s+const\s+[^\s=]+Config\s*=\s*(\{[\s\S]*?\})\s*;?\s*$/m);

        if (match) {
          try {
            // 对于简单的配置（没有模板字符串），直接使用eval
            config = eval(`(${match[1]})`);
          } catch (evalError) {
            console.log("Eval failed:", evalError.message);
            // 如果eval失败，返回null让它使用默认配置
            config = null;
          }
        }
      }

      if (config) {
        console.log("Successfully parsed config:", config.model);
        return NextResponse.json(config);
      } else {
        console.log("Failed to parse config, using default");
        throw new Error("Could not parse rule configuration");
      }
    } catch (error) {
      console.error("Rule loading error:", error.message);
      // 如果文件不存在，返回默认配置
      return NextResponse.json({
        model: "gemini-2.0-flash-exp",
        config: {
          systemInstruction: `你是一个专业的内容生成助手。`,
        }
      });
    }
  } catch (error) {
    console.error("获取规则失败:", error);
    return NextResponse.json(
      { error: "Failed to get rule" },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ name: string }> }
) {
  try {
    const { name } = await params;
    const config = await request.json();

    const rulePath = path.join(process.cwd(), "rules", `${name}.js`);

    // 将配置转换为 JS 文件格式
    // 对于包含特殊字符的名称，直接使用原名称（JavaScript 支持 Unicode 标识符）
    const content = `export const ${name}Config = ${JSON.stringify(config, null, 2)};
`;

    await fs.writeFile(rulePath, content, "utf-8");

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("保存规则失败:", error);
    return NextResponse.json(
      { error: "Failed to save rule" },
      { status: 500 }
    );
  }
}

